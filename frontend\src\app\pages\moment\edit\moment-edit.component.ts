import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Date<PERSON><PERSON><PERSON><PERSON>, MAT_DATE_LOCALE } from '@angular/material/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { Moment } from '../../../models/moment';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import { CommonUtil } from '../../../shared/common.util';
import { MomentManager } from '../moment.manager';

import { FarmManager } from '../../farm/farm.manager';
import { Farm } from '../../../models/farm';
import { UserAssignTrainingManager } from '../../userassigntraining/userassigntraining.manager';
import { UserAssignTraining } from '../../../models/userassigntraining';
import { CommonEventService } from '../../../shared/common.event.service';
import { RestResponse } from 'src/app/shared/auth.model';
import { LoadVideoFromUrl } from 'src/app/shared/load-video-from-url';
import { FilterParam } from 'src/app/models/filterparam';
import { FarmService } from '../../farm/farm.service';
import { ManageCourseService } from '../../manage-course/manage-course-service';
import { Course } from 'src/app/models/course';
import { Training } from 'src/app/models/training';
import { CourseTrainingsService } from '../../course-trainings/course-trainings-service';
import { Constant } from '../../../config/constants';
declare const $: any;

@Component({
  selector: 'app-moment-edit',
  templateUrl: './moment-edit.component.html',
  styleUrls: ['./moment-edit.component.scss'],
  providers: [
    { provide: MAT_DATE_LOCALE, useValue: 'en-GB' }
  ]
})

export class MomentEditComponent extends BaseEditComponent implements OnInit, OnDestroy {

  public moment: Moment;
  public farms: Farm[];
  public courses: Course[];
  public trainings: Training[];
  public userAssignTrainings: UserAssignTraining[];
  public uploader: any;
  public mediaType: any;
  public loadingVideo: boolean = false;
  public videoPlaying: boolean = false;
  private fileData: any;
  public isLoader: boolean;
  private intervalId: NodeJS.Timeout;
  fileUploadingMessage: string = "UPLOADING..";
  selectedType: string | null = null;
  showTypeOptions: boolean = false;
  recurringOptions: any[] = [];
  typeOptions: any[] = [];
  titleTrimError: boolean = false;
  descriptionTrimError: boolean = false;

  constructor(protected route: ActivatedRoute,
    protected momentManager: MomentManager,
    protected toastService: ToastService,
    protected loadingService: LoadingService,
    protected router: Router,
    protected commonService: CommonService,
    public authService: AuthService,
    protected translateService: TranslateService,
    private farmManager: FarmManager,
    private userassigntrainingManager: UserAssignTrainingManager,
    public commonUtil: CommonUtil,
    private loadVideoFromUrl: LoadVideoFromUrl,
    protected farmService: FarmService,
    protected courseService: ManageCourseService,
    protected courseTrainingsService: CourseTrainingsService,
    private dateAdapter: DateAdapter<Date>
  ) {
    super(momentManager, commonService, toastService, loadingService, route, router, translateService);
    // Ensure datepicker displays and parses dates as dd/MM/yyyy
    this.dateAdapter.setLocale('en-GB');
  }

  ngOnInit() {
    this.moment = new Moment();
    this.moment.isActive = true;
    this.setRecord(this.moment);
    this.uploader = this.initializeUploader(null, 'jpg,png,jpeg,mkv,mp4', null, null, this.toastService, "Only Jpeg, Jpg, Png, Mp4, Mkv are allowed", null)
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
    this.farms = new Array<Farm>();
    this.userAssignTrainings = new Array<UserAssignTraining>();
    this.recurringOptions = Constant.MOMENT_OPTIONS.RECURRING_OPTIONS;
    this.typeOptions = Constant.MOMENT_OPTIONS.TYPE_OPTIONS;
    this.init();
    this.getMediaType(this.moment.mediaUrl);
    // Set min/max date if needed
    this.minToDate = new Date(); // today as minimum
    this.maxToDate = null; // or set a max date if required
  }
  
  minToDate: Date | null = null;
  maxToDate: Date | null = null;
  minFromDate: Date | null = new Date();
  maxFromDate: Date | null = null;
  
  toDateChange(type: string, event: any) {
    if (event && event.value) {
      if (this.moment.startDate && event.value < this.moment.startDate) {
        this.toastService.error('End date cannot be less than start date.');
        this.moment.closedDate = null;
        return;
      }
      this.moment.closedDate = event.value;
    }
  }
  
  clearToDate(event: Event) {
    event.stopPropagation();
    this.moment.closedDate = null;
  }

  fromDateChange(type: string, event: any) {
    if (event && event.value) {
      this.moment.startDate = event.value;
    }
  }

  clearFromDate(event: Event) {
    event.stopPropagation();
    this.moment.startDate = null;
  }

  getMediaType(url) {
    if (url) {
      const extension = url.split(/[#?]/)[0].split('.').pop().trim();
      if (extension == "jpg" || extension == "jpeg" || extension == "png") {
        this.mediaType = "image"
      }
      if (extension == "mkv" || extension == "mp4" || extension == "avi" || extension == 'avi' || extension == "mov") {
        this.mediaType = "video";
      }
    }
  }

  ngOnDestroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  onFetchCompleted() {
    this.moment = Moment.fromResponse(this.record);
    this.setRecord(this.moment);
    this.getMediaType(this.moment.mediaUrl);
  }

  uploadMomentVideoOrImage(event: any) {
    if (event) {
      this.isLoader = true;
      const file = event.target.files[0];
      if (event.target.files[0].type != "video/mp4" && event.target.files[0].type != "image/jpg" && event.target.files[0].type != "image/jpeg"
        && event.target.files[0].type != "image/png") {
        this.commonService.convertVideoFormat(file).then(res => {
          this.fileData = {} as any;
          this.fileData.files = [] as any;
          this.fileData.files.push(res);
          this.onFileProcessingCompleted(this.fileData.files);
        });
      }
      else {
        this.fileData = {} as any;
        this.fileData.files = event.target.files;
        this.onFileProcessingCompleted(this.fileData.files);
      }
    }
  }

  removeFile() {
    this.commonService.confirmation('Would you like to delete?', this.removeFileCallback.bind(this), null);
  }

  removeFileCallback() {
    this.moment.mediaUrl = "";
    this.videoPlaying = false;
  }

  playVideoFromPlayIcon() {
    var videoId = document.getElementById("videoId") as HTMLVideoElement | null;
    if (videoId != null) {
      if (videoId.paused) {
        videoId.play();
        videoId.controls = true
      }
    }
    videoId.addEventListener("playing", (event) => {
      videoId.controls = true;
      this.videoPlaying = true;
    });
    videoId.addEventListener("ended", (event) => {
      videoId.controls = false;
      this.videoPlaying = false;

    });
  }

  async onUploadSuccess(file: any, files: any) {
    let gumletResponse = null;

    const filePath = file.streamingPath ? file.streamingPath : file.path;

    this.getMediaType(filePath);

    if (file.streamingId && this.mediaType == 'video') {

      this.filterParam.gumletId = file.streamingId

      gumletResponse = await this.commonService.getGumletResponse(this.filterParam);

      this.intervalId = setInterval(async () => {
        gumletResponse = await this.commonService.getGumletResponse(this.filterParam);
        if (gumletResponse.status) {

          this.fileUploadingMessage = gumletResponse.data.status ? gumletResponse.data.status.toUpperCase() + ".." : this.fileUploadingMessage = "UPLOADING..";

          if (gumletResponse.data.status == "ready" || gumletResponse.data.status == "queued" || gumletResponse.data.status == "downloading" || gumletResponse.data.status == "downloaded" || gumletResponse.data.status == "validating") {

            if (this.intervalId) {
              clearInterval(this.intervalId);
            }
            this.moment.mediaUrl = filePath;
            this.fileUploadingMessage = "UPLOADING.."
            this.isLoader = false;
          }
        } else {
          if (this.intervalId) {
            clearInterval(this.intervalId);
          }
          this.moment.mediaUrl = file.path;
          this.fileUploadingMessage = "UPLOADING.."
          this.isLoader = false;
        }
      }, 5000);
    } else {
      this.isLoader = false;
      this.moment.mediaUrl = filePath;
    }
  }

  async fetchAssociatedData() {
    this.filterParam = new FilterParam();
    const farms: RestResponse = await this.farmService.fetchAvailableFarms(this.filterParam);
    this.farms = farms.data;
  }

  async fetchCoursesandTrainings() {
    if (!this.moment.farmId) {
      this.courses = [];
      this.trainings = [];
      return;
    }

    this.filterParam = new FilterParam();
    this.filterParam.relationTable = "FARM";
    this.filterParam.relationId = this.moment.farmId;
    // Fetch courses for selected farm

    const trainingResp: RestResponse = await this.courseTrainingsService.fetchAvailableTrainings(this.filterParam);
    this.trainings = trainingResp.data || [];
    console.log("trainings", this.trainings);
    const courseResp: RestResponse = await this.courseService.fetchAvailableCourses(this.filterParam);
    this.courses = courseResp.data || [];
    console.log("courses", this.courses);
    // Fetch trainings for selected farm
  }

  afterFetchAssociatedCompleted() {
    const farmIdId: string = this.route.snapshot.queryParamMap.get('Farm');
    if (farmIdId) {
      this.onAssociatedValueSelected({ "id": farmIdId }, 'momentFarmIdSelect');
    }
    const assignTrainingIdId: string = this.route.snapshot.queryParamMap.get('UserAssignTraining');
    if (assignTrainingIdId) {
      this.onAssociatedValueSelected({ "id": assignTrainingIdId }, 'momentAssignTrainingIdSelect');
    }
  }

  onSaveSuccess(message: any) {
    this.toastService.success(message);
    this.navigate('/dashboard/moments');
  }

  checkConditionToReload(records: BaseModel[], selectedRecord: any) {
    if (!records.some(x => x.id === selectedRecord.id)) {
      this.fetchAssociatedData();
    }
  }

  onAssociatedValueSelected(selectedRecord: any, selectedField: any) {
    if (this.request.popupId) {
      $('#' + this.request.popupId).appendTo('body').modal('hide');
    }
    if ((!this.isNullOrUndefined(selectedField) && selectedField === 'momentFarmIdSelect') || this.request.popupId === 'momentFarmIdPopup') {
      this.moment.farmId = selectedRecord.id;
      this.checkConditionToReload(this.farms, selectedRecord);
      return;
    }
    if ((!this.isNullOrUndefined(selectedField) && selectedField === 'momentAssignTrainingIdSelect') || this.request.popupId === 'momentAssignTrainingIdPopup') {
      this.moment.assignTrainingId = selectedRecord.id;
      this.checkConditionToReload(this.userAssignTrainings, selectedRecord);
      return;
    }

  }
  fileUploadValidationBeforeSave(): boolean {
    if (!this.moment.mediaUrl) {
      this.onClickValidation = true;
      return false;
    }
    return true;
  }
  onFileProcessingCompleted(files) {
    this.uploader.addToQueue(files);
    this.uploader.uploadAll();
  }

  onFarmSelect(farmId: string) {
    const farmDetail: any[] = [];

    if (this.moment.farmDetail && this.moment.farmDetail.length > 0) {
      const prevFarm = this.moment.farmDetail[0];
      if (prevFarm.id !== farmId && !prevFarm.isDeleted) {
        farmDetail.push({ ...prevFarm, isDeleted: true });
      }
    }

    const selectedFarm = this.farms.find(f => f.id === farmId);
    if (selectedFarm) {
      farmDetail.push({ ...selectedFarm, isDeleted: false });
    }

    this.moment.farmDetail = farmDetail;
  }


  onFarmsChange(selectedFarmIds: string[]) {
    const prevFarmDetails = this.moment.farmDetail || [];

    // Use only ids of non-deleted farm details
    const prevFarmIds = prevFarmDetails.filter(fd => !fd.isDeleted).map(fd => fd.id);

    const removedFarmIds = prevFarmIds.filter(id => !selectedFarmIds.includes(id));
    const addedFarmIds = selectedFarmIds.filter(id => !prevFarmIds.includes(id));

    // Mark removed farms
    let updatedFarmDetails = prevFarmDetails.map(fd => {
      if (removedFarmIds.includes(fd.id)) {
        return { id: fd.id, isDeleted: true };
      }
      return { id: fd.id, isDeleted: false };
    });

    // Add new farms (only id)
    addedFarmIds.forEach(id => {
      updatedFarmDetails.push({ id, isDeleted: false });
    });

    this.moment.farmDetail = updatedFarmDetails;
  }

  validateTrimmedField(fieldName: string, value: string) {
    if (fieldName === 'title') {
      this.titleTrimError = !value || value.trim() === '';
    } else if (fieldName === 'description') {
      this.descriptionTrimError = !value || value.trim() === '';
    }
  }

  async save(form: any) {
    if (this.moment.startDate && this.moment.closedDate && this.moment.closedDate < this.moment.startDate) {
      this.toastService.error('End date cannot be less than start date.');
      return;
    }

    // Validate trimmed fields
    this.validateTrimmedField('title', this.moment.title);
    this.validateTrimmedField('description', this.moment.description);

    this.onClickValidation = !form.valid || this.titleTrimError || this.descriptionTrimError;
    if (!form.valid || this.titleTrimError || this.descriptionTrimError) {
      return;
    }

    const fileValidation = this.fileUploadValidationBeforeSave();

    if (!fileValidation) {
      return
    }

    if (!this.record.isValidateRequest(form, this.toastService, this.translateService)) {
      return;
    }

    // Remove farmIds from payload if present (should not be sent)
    if ((this.moment as any).farmIds) {
      delete (this.moment as any).farmIds;
    }
    if ((this.record as any).farmIds) {
      delete (this.record as any).farmIds;
    }
    // Also ensure farmIds is not present in the object passed to the API
    const payload = { ...this.record.forRequest() };
    if ((payload as any).farmIds) {
      delete (payload as any).farmIds;
    }
    try {
      this.loadingService.show();
      const method = this.request.isNewRecord ? 'save' : 'update';
      const response: RestResponse = await this.manager[method](payload);
      this.loadingService.hide();
      if (!response.status) {
        this.toastService.error(response.message);
        return;
      }
      this.onSaveSuccess(response.message);
    } catch (error) {
      this.loadingService.hide();
      this.toastService.error(error.message);
    }
  }

  toggleType(type: 'COURSE' | 'TRAINING') {
    this.selectedType = type;      // Optionally clear selection when switching type
    if (type === 'COURSE') {
      this.moment.training = null;
    } else if (type === 'TRAINING') {
      this.moment.course = null;
    }
  }

  onShowTypeOptionsChange(value: boolean) {
    if (value) {
      this.fetchCoursesandTrainings();
    }
  }

}
