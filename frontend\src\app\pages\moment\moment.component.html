<div [ngClass]="{'momentsByUserIdCls': momentsByUserId}" data-aos="fade-up" data-aos-duration="1000"
  class="site-customer-main-container" id="manage-moments">
  <div class="manage-moment-section d-flex align-items-center justify-content-between">
    <div class="custom-input-group mb-0">
      <input class="form-control search-form-control margin-bottom-10" placeholder="Search" appDelayedInput
        (delayedInput)="search($event)" [delayTime]="1000" [(ngModel)]="searchMoment">
      <i class="bi bi-search pe-3"></i>
    </div>
    <div class="d-flex align-items-center margin-bottom-10">
      <div class="manage-moment-button me-2">
        <button [disabled]="isMomentCsvExport ? true : false" (click)="exportMomentsCsv()" type="button"
          class="btn manage-filter-buttton export-btn btn-secondary text-light btn-lg d-flex height-51px font-15px">
          <i class="icon-export bi bi-box-arrow-down me-2" alt=""></i><span class="margin-top-2">{{isMomentCsvExport ?
            'Please Wait...' : 'Export Report'}} </span>
        </button>
      </div>
      <div class="manage-moment-button">
        <button (click)="openFilterMomentModal()" type="button"
          class="btn manage-filter-buttton me-2 bg-dark text-light btn-lg font-15px height-51px filter-button-cls">
          <img src="/assets/images/icons/menu/filter.svg" class="me-2 img-fluid moment-icon width-15px" alt="">Filter
        </button>
      </div>
      <div *ngIf="!momentsByUserId" class="manage-moment-button">
        <button [routerLink]="['/dashboard/moment/edit/0']" type="button"
          class="btn manage-filter-buttton manage-moment-add-btn btn-secondary text-light btn-lg font-15px height-51px">
          <img src="/assets/images/icons/menu/add_icon.svg" class="me-2 moment-icon width-15px" alt="">Add
        </button>
      </div>
    </div>
  </div>

  <div class="table-responsive server-side-table" [ngClass]="{'has-records':records.length>0}">
    <table class="table table-borderless" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
      <tbody>
        <div [ngClass]="{'mt-4': i > 0, 'recurring-moment-cls': record.isRecurring}" class="box d-table w-100  p-4"
          *ngFor="let record of records, index as i">
          <tr class="white-space">
            <td style="vertical-align: top;">
              <h6 class="semi-bold mb-1">Title</h6>
              <span class="d-inline-flex align-items-center gap-2">
                <span class="cursor-pointer"
                  [routerLink]="['/dashboard/moment/detail/'+record.id]">{{record.title}}</span>
                <span [ngClass]="record.isRecurring ? 'recurring-badge' : 'nonrecurring-badge'" class="badge ms-2">
                  {{ record.isRecurring ? 'Recurring' : 'Non-Recurring' }}
                </span>

              </span>
            </td>
            <td style="width: 100px;">
              <h6 class="semi-bold">Created On</h6>
              <p class="height-63px">{{moment(record.createdOn).format('DD-MM-YYYY')}}</p>
            </td>

            <td style="width: 100px;">
              <h6 class="semi-bold">Last Updated On</h6>
              <p class="height-63px">{{moment(record.updatedOn).format('DD-MM-YYYY')}}</p>
            </td>
            
            <td *ngIf="record.isRecurring" style="width: 120px;">
              <h6 class="semi-bold">End Date</h6>
              <p class="height-63px">{{record.closedDate ? (moment(record.closedDate).format('DD-MM-YYYY')) : '-'}}</p>
            </td>

            <td style="width: 120px;">
              <span>
                <h6 class="semi-bold">Status</h6>
                <button class="confirmation-icon cursor-auto" *ngIf="record.status == MY_CONSTANT.MOMENT_STATUS.OPEN ">
                  <img src="/assets/images/icons/menu/confirmation.svg" class="me-1"
                    alt="">{{MY_CONSTANT.MOMENT_STATUS.OPEN}}
                </button>
                <div class="width-100-px" *ngIf="record.status == MY_CONSTANT.MOMENT_STATUS.WAITING_FOR_APPROVAL">
                  <i (click)="approveOrRejectMoment(record,true,false)"
                    class="bi bi-check-circle fs-3 cursor-pointer"></i>
                  <i (click)="approveOrRejectMoment(record,false,false)"
                    class="bi bi-x-circle ps-2 fs-3 cursor-pointer"></i>
                </div>
                <button class="close-icon cursor-auto" *ngIf="record.status == MY_CONSTANT.MOMENT_STATUS.CLOSED">
                  <img class="me-1" src="/assets/images/icons/menu/cancel.svg">{{MY_CONSTANT.MOMENT_STATUS.CLOSED}}
                </button>
                <button class="action-taken-icon" *ngIf="record.status == MY_CONSTANT.MOMENT_STATUS.ACTION_TAKEN"
                  data-bs-toggle="modal" [attr.data-bs-target]="'#actionTakenModal' + record.id"
                  (click)="openActionTakenModal(record)">
                  <img class="me-1" src="/assets/images/icons/menu/action-taken.svg" alt="">ACTION TAKEN
                </button>
                <p class="height-25px"></p>
              </span>
            </td>

            <td style="width: 100px;">
              <h6 class="semi-bold">Created By</h6>
              <p class="height-63px">{{record.createdByUserDetail?.fullName}}</p>
            </td>

            <td style="width: 120px;">
              <h6 class="semi-bold text-end">Action</h6>
              <div class="text-start float-end d-flex">
                <!-- <span class="width-95-px" *ngIf="record.status == MY_CONSTANT.MOMENT_STATUS.CLOSED" style="margin-right:65px;">
                  <h6>Completed By</h6>
                  <p class="pre-line">{{record?.userIdDetail?.fullName}}</p>
                </span> -->
                <div class="d-flex justify-content-end">
                  <span title = "Close Moment" *ngIf="record.recurringLogsCount > 0 && record.status !== MY_CONSTANT.MOMENT_STATUS.CLOSED"
                    (click)="approveOrRejectMoment(record,true,true)" class="LogsIconMoment font-23px">
                    <i class="bi bi-x-circle font-23px cursor-pointer"></i>
                  </span>
                  <span title = "View Rejected Logs" (click)="openMomentRejectedModal(record.id)" class="LogsIconMoment"
                    *ngIf="record.rejectLogsCount > 0">
                    <img src="/assets/images/icons/menu/pending-log.svg">
                  </span>
                  <span title = "View Detail" class="moment-detail-cls" [routerLink]="['/dashboard/moment/detail/'+record.id]"> <i
                      class="bi bi-eye"></i>
                  </span>
                  <!-- <span
                    *ngIf="!record.isRecurring && !record.userVideo && record.status !== MY_CONSTANT.MOMENT_STATUS.CLOSED"
                    [routerLink]="['/dashboard/moment/edit/'+record.id]" class="fs-5 cursor-pointer me-2">
                    <i class="bi bi-pencil-fill"></i>
                  </span> -->
                  <i title = "Delete" (click)="remove(record.id)" class="bi bi-trash fs-5 cursor-pointer"></i>
                </div>
              </div>
              <p class="height-63px"></p>
            </td>
          </tr>
          <tr class="white-space">
            <!-- <td>
              <h6>Video Assigned</h6>
              <a *ngIf="record.assignTrainingId && record?.TrainingIdDetail?.length else noTrainingAssigned"
                class="text-decoration-underline text-secondary d-block height-30px"
                (click)="watchVideo(record.TrainingIdDetail)"><img src="/assets/images/icons/menu/video-logo.svg"
                  class="me-1" alt=""> Watch
                Video</a> 
              <ng-template #noTrainingAssigned><a class="d-block height-30px">-</a></ng-template>
            </td> -->
            <td style="width: 120px;vertical-align: baseline;" *ngIf="record.courseTitle">
              <h6 class="semi-bold">Course</h6>
              <p class="height-63px">{{record.courseTitle}}</p>
            </td>

            <td style="width: 120px;vertical-align: baseline;" *ngIf="record.trainingTitle">
              <h6 class="semi-bold">Training</h6>
              <p class="height-63px">{{record.trainingTitle}}</p>
            </td>

            <td style="width: 120px;vertical-align: baseline;">
              <h6 class="semi-bold">Company</h6>
              <ng-container *ngIf="record.farmDetail?.length; else noFarms">
                <span class="badge badge-pill bg-light text-dark farm-badge">
                  {{record.farmIdDetail?.farmCode}} {{ record.farmDetail[0]?.name }}
                </span>
                <span *ngIf="record.farmDetail.length > 1"
                  class="badge badge-pill bg-secondary text-white farm-badge cursor-pointer"
                  (click)="openCompanyModal(record.farmDetail)">
                  +{{ record.farmDetail.length - 1 }} more
                </span>
              </ng-container>
              <ng-template #noFarms>
                <span class="text-muted small">No Companies</span>
              </ng-template>
            </td>

            <td style="width: 120px;vertical-align: baseline;" *ngIf="record.isRecurring">
              <h6 class="semi-bold">Logs</h6>
              <button class="btn btn-primary btn-sm d-flex align-items-center gap-2 rounded-pill px-3 py-2"
                (click)="openLogsModal(record)">
                <i class="bi bi-list-ul"></i>
                <span>View Logs</span>
              </button>
            </td>

            <td style="width: 120px;vertical-align: baseline;">
              <h6 class="semi-bold">Moment Video/Image</h6>
              <a class="text-decoration-underline text-secondary d-block height-30px"
                (click)="openImageOrVideo(record)"><img
                  [src]="'/assets/images/icons/menu/'+ this.getMomentMediaType(record.mediaUrl, 'icon')" class="me-1"
                  [ngClass]="{'width-27px': this.getImageWidthClass(record.mediaUrl)}"
                  alt="">{{getMomentMediaType(record.mediaUrl,
                "title")}}</a>
            </td>
            <td *ngIf="!record.isRecurring && record.userVideo">
              <h6 class="semi-bold">Staff capture Video/Image</h6>
              <a *ngIf="(record.userVideo && record.status != MY_CONSTANT.MOMENT_STATUS.OPEN && !record.isRecurring) || (record.recurringLogsCount > 0) else noStaffVideo"
                class="text-decoration-underline d-block height-30px text-secondary"
                (click)="!record.isRecurring ? openStaffImageOrVideo(record) : openMomentRecurringModal(record.id)"><img
                  [src]="!record.isRecurring ? '/assets/images/icons/menu/'+ this.getMomentMediaType(record.userVideo, 'icon') : '/assets/images/icons/menu/list-solid.svg'"
                  class="me-1"
                  [ngClass]="{'width-27px': !record.isRecurring && this.getImageWidthClass(record.userVideo), 'recurring-moment-list-cls': record.isRecurring}"
                  alt="">{{!record.isRecurring ? this.getMomentMediaType(record.userVideo,"title") : 'View List'}}</a>
              <ng-template #noStaffVideo><a class="d-block height-30px">-</a></ng-template>
            </td>

            <td class="d-flex float-end pe-0">

            </td>
          </tr>
        </div>
      </tbody>
    </table>
    <div class="modal fade modal-xl" id="trainingListModal" aria-hidden="true" aria-labelledby="trainingListModal"
      tabindex="-1">
      <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="trainingListModalLabel">{{
              !assignVideoData?.assignTrainingId ? 'Assign Training Video' : 'Change Training Assign Video'}}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <app-training [assignTrainingIdFromMoment]="assignVideoData?.assignTrainingId"
              (assignTrainingIdOutput)="assignTrainingIdOutputCallback($event)" [assignVideoFromMoment]="true"
              *ngIf="trainingListModal && trainingListModal._isShown"></app-training>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
    <div class="modal fade modal-xl" id="momentRejectLogsModal" aria-hidden="true"
      aria-labelledby="momentRejectLogsModal" tabindex="-1">
      <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="momentRejectLogsModalLabel">Moment Rejected Logs List</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <app-moment-rejected-logs-list [getMomentId]="momentData && momentData"
              *ngIf="momentRejectLogsModal && momentRejectLogsModal._isShown"></app-moment-rejected-logs-list>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
    <div class="modal fade modal-xl" id="momentRecurringLogsModal" aria-hidden="true"
      aria-labelledby="momentRecurringLogsModal" tabindex="-1">
      <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="momentRecurringLogsModalLabel">Moment Recurring Logs List</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <app-recurring-moment-logs [getMomentId]="momentData && momentData"
              *ngIf="momentRecurringLogsModal && momentRecurringLogsModal._isShown"></app-recurring-moment-logs>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
    <div class="modal fade modal-xl" id="instructionsModal" aria-hidden="true" aria-labelledby="instructionsModal"
      tabindex="-1">
      <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="instructionsModalLabel">{{getEnglishInstructionCheck(momentData) ?
              'View Instructions' : 'Add Instructions'}}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body" *ngIf="instructionsModal && instructionsModal._isShown">
            <div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent">
              <div class="dashboard-content-container">
                <form #instructionForm="ngForm" novalidate="novalidate">
                  <div class="row">
                    <div *ngFor="let data of instructionsFormInputs" class="col-12 col-md-6 col-lg-6 col-xl-4">
                      <div class="category-language w-100 mb-4 p-3 bg-secondary d-flex justify-content-between">
                        <h6 class="lh-lg program-heading text-light">{{data.languageName}}</h6>
                        <img [src]="getImageFetchByLanguageName(data.languageName)" class="img-fluid me-2" alt="">
                      </div>
                      <div class="video-title">
                        <div class="form-floating form-floating-textarea mb-4 w-100 category-language">
                          <textarea (keyup)="addInstructionToRemainingLanguages(data.instruction,data.languageName)"
                            [disabled]="momentData?.status == MY_CONSTANT.MOMENT_STATUS.CLOSED ? true : false"
                            [ngClass]="{'is-invalid':!instruction.valid && onClickValidation}"
                            class="form-control form-description" [name]="data.languageName+'Instruction'"
                            #instruction="ngModel" [(ngModel)]="data.instruction"
                            [required]="data.languageName == MY_CONSTANT.languages.English ? true : false"
                            placeholder="Instruction" id="floatingTextarea2"></textarea>
                          <label for="floatingInput">{{"Moment.Instruction" | translate}}</label>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-12 col-xxl-12 mt-4 d-flex justify-content-end">
                      <button [disabled]="momentData?.status == MY_CONSTANT.MOMENT_STATUS.CLOSED ? true : false"
                        class="btn btn-secondary site-button btn-sm large-button save-button rounded-3" type="button"
                        (click)="save(instructionForm.form)">
                        Save
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
    <div class="modal fade" id="filterMomentModal" tabindex="-1" aria-labelledby="filterMomentModalLabel"
      aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="filterMomentModalLabel">Filter Moment</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div *ngIf="filterMomentModal && filterMomentModal._isShown" class="modal-body">
            <form #momentFilterForm="ngForm" novalidate="novalidate">
              <div class="form-floating mb-3">
                <select class="form-select form-control" name="moment_status" aria-label="Please Select status"
                  [(ngModel)]="filterParam.status" [ngClass]="{'is-invalid':!status.valid && onClickValidation}"
                  required="required" #status="ngModel">
                  <option [ngValue]="undefined" selected disabled>Select Option</option>
                  <option value="ACTION_TAKEN">ACTION TAKEN</option>
                  <option value="OPEN">OPEN</option>
                  <option value="CLOSED">CLOSED</option>
                </select>
                <label for="type">{{"Moment.status" | translate}}</label>
              </div>
              <div class="form-floating mb-3">
                <select class="form-select form-control" name="moment_type" aria-label="Select Moment Type"
                  [(ngModel)]="filterParam.isRecurring"
                  [ngClass]="{'is-invalid':!isRecurring.valid && onClickValidation}" required="required"
                  #isRecurring="ngModel">
                  <option [ngValue]="undefined" selected disabled>Select Option</option>
                  <option value="true">Recurring Moment</option>
                  <option value="false">Non Recurring Moment</option>

                </select>
                <label for="type">{{"Moment.SelectType" | translate}}</label>
              </div>
              <app-date-range-filter (fromDateOutput)="fromDateOutput($event)" (toDateOutput)="toDateOutput($event)"
                [fromDateInput]="filterParam.startDate" [toDateInput]="filterParam.endDate"></app-date-range-filter>
              <div class="modal-footer">
                <button (click)="resetFilter()" type="button" class="text-white btn btn-secondary">Reset</button>
                <button (click)="onClickMomentFilter(momentFilterForm.form.valid)" type="button"
                  class="btn btn-primary">Filter</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
    <div class="modal fade" id="momentAssignedTrainingVideo" aria-hidden="true"
      aria-labelledby="momentAssignedTrainingVideo" tabindex="-1">
      <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="momentAssignedTrainingVideoLabel">{{recordData?.videoTitle}}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body" *ngIf="momentAssignedTrainingVideo && momentAssignedTrainingVideo._isShown">
            <div *ngIf="loadingVideo" class="loading-container-video-training">
              <span class="text-white" style="font-size:25px; margin-right: 11px">Loading Video</span>
              <div class="spinner-border text-light" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
            <video playsinline autoplay [ngClass]="{'d-none': loadingVideo, 'd-block': !loadingVideo}" controls
              id="staff-video"></video>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
    <div class="modal fade" id="momentVideoOrImageModal" aria-hidden="true"
      aria-labelledby="momentVideoOrImageModalLabel" tabindex="-1">
      <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="momentVideoOrImageModalLabel">{{recordData?.farmIdDetail?.name}}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body" *ngIf="momentVideoOrImageModal && momentVideoOrImageModal._isShown">
            <div *ngIf="recordData?.mediaType == 'video'">
              <div *ngIf="loadingVideo" class="loading-container-video-training">
                <span class="text-white" style="font-size:25px; margin-right: 11px">Loading Video</span>
                <div class="spinner-border text-light" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
              </div>
              <video playsinline autoplay [ngClass]="{'d-none': loadingVideo, 'd-block': !loadingVideo}" controls
                id="staff-video"></video>
            </div>
            <div *ngIf="recordData?.mediaType == 'image'">
              <img class="img-fluid" [src]="recordData?.mediaUrl" />
            </div>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
    <div class="modal fade" id="staffVideoOrImageModal" aria-hidden="true" aria-labelledby="staffVideoOrImageModalLabel"
      tabindex="-1">
      <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="staffVideoOrImageModalLabel">{{recordData?.farmIdDetail?.name}}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body" *ngIf="staffVideoOrImageModal && staffVideoOrImageModal._isShown">
            <div *ngIf="recordData?.mediaType == 'video'">
              <div *ngIf="loadingVideo" class="loading-container-video-training">
                <span class="text-white" style="font-size:25px; margin-right: 11px">Loading Video</span>
                <div class="spinner-border text-light" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
              </div>
              <video playsinline autoplay [ngClass]="{'d-none': loadingVideo, 'd-block': !loadingVideo}" controls
                id="staff-video"></video>
            </div>
            <div *ngIf="recordData?.mediaType == 'image'">
              <img class="img-fluid" [src]="recordData?.userVideo" />
            </div>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="logsModal" tabindex="-1" aria-labelledby="logsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content logs-modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="logsModalLabel">Recurring Logs</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body logs-modal-body">
        <div *ngIf="selectedRecord">
          <div *ngIf="selectedRecord.isRecurring && selectedRecord.recurringLogs?.length">
            <table class="table table-bordered table-striped logs-table">
              <thead>
                <tr>
                  <th style="width: 40px;">#</th>
                  <th>Name</th>
                  <th>Moment Created Date</th>
                  <th>Staff Uploaded Date</th>
                  <th>Video/Image</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let log of selectedRecord.recurringLogs; let i = index">
                  <td>{{ i + 1 }}</td>
                  <td>{{ log.userDetail?.fullName || '-' }}</td>
                  <td>{{ log.momentCreatedDate ? (log.momentCreatedDate | date:'dd-MM-yyyy HH:mm') : '-' }}</td>
                  <td>{{ log.staffUploadedVideoDate ? (log.staffUploadedVideoDate | date:'dd-MM-yyyy HH:mm') : '-' }}
                  </td>
                  <td>
                    <a *ngIf="log.userVideoUrl" href="javascript:void(0)"
                      (click)="showPreview(log.userVideoUrl)">View</a>
                    <span *ngIf="!log.userVideoUrl">-</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div
            *ngIf="(!selectedRecord.isRecurring && !selectedRecord.rejectedLogs?.length) || (selectedRecord.isRecurring && !selectedRecord.recurringLogs?.length)">
            <span>No logs found for this moment.</span>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Company List Modal -->
<div class="modal fade" id="companyListModal" tabindex="-1" aria-labelledby="companyListModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="companyListModalLabel">Companies</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <ul class="list-group">
          <li class="list-group-item" *ngFor="let company of companiesToShow">{{ company.name }}</li>
        </ul>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="previewModalLabel">Preview</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body text-center preview-modal-body">
        <ng-container *ngIf="previewType === 'image'">
          <img [src]="previewUrl" class="img-fluid" alt="Preview" />
        </ng-container>
        <ng-container *ngIf="previewType === 'video'">
          <video [src]="previewUrl" controls class="w-100" style="max-height: 400px;"></video>
        </ng-container>
        <ng-container *ngIf="!previewType">
          <p>Cannot preview this file type.</p>
        </ng-container>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="actionTakenModal" tabindex="-1" aria-labelledby="actionTakenModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="actionTakenModalLabel">Change Moment Status</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Do you want to close/open moment?</p>
        <div class="d-flex justify-content-end gap-2">
          <button class="btn btn-success"
            (click)="changeMomentStatus('OPEN', selectedRecordForAction?.id)">Open</button>
          <button class="btn btn-danger"
            (click)="changeMomentStatus('CLOSED', selectedRecordForAction?.id)">Close</button>
        </div>
      </div>
    </div>
  </div>
</div>