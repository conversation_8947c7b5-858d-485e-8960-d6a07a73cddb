<!-- Main Container -->
<div class="site-customer-main-container" id="manage-moments">
  <div class="moment-detail-card">
    <!-- Modern Header -->
    <div class="modern-header d-flex align-items-center mb-3 justify-content-between flex-wrap">
      <div class="d-flex align-items-center flex-grow-1">
        <div class="modern-accent-bar me-3"></div>
        <span class="moment-detail-title me-2">{{record?.title}}</span>
        <ng-container [ngSwitch]="record.status">
          <!-- <span *ngSwitchCase="MY_CONSTANT.MOMENT_STATUS.OPEN"
            class="badge bg-success px-3 py-2 fs-5 moment-detail-status shadow-sm rounded-pill align-middle ms-2">
            <img src="/assets/images/icons/menu/confirmation.svg" class="me-1 align-text-top" alt="Open Status Icon">
            {{MY_CONSTANT.MOMENT_STATUS.OPEN}}
          </span> -->
          <span *ngSwitchCase="MY_CONSTANT.MOMENT_STATUS.OPEN">
            <button class="confirmation-icon cursor-auto" *ngIf="record.status == MY_CONSTANT.MOMENT_STATUS.OPEN ">
              <img src="/assets/images/icons/menu/confirmation.svg" class="me-1"
                alt="">{{MY_CONSTANT.MOMENT_STATUS.OPEN}}
            </button>
          </span>
          <span *ngSwitchCase="MY_CONSTANT.MOMENT_STATUS.WAITING_FOR_APPROVAL"
            class="badge bg-warning px-3 py-2 fs-5 moment-detail-status shadow-sm rounded-pill align-middle ms-2">
            <img src="/assets/images/icons/menu/refresh.svg" class="me-1 align-text-top"
              alt="Waiting for Approval Icon">
            WAITING FOR APPROVAL
          </span>
          <span *ngSwitchCase="MY_CONSTANT.MOMENT_STATUS.CLOSED"
            class="close-icon cursor-auto d-inline-flex align-items-center px-2 py-1 rounded-pill bg-light">
           <img class="me-1 align-middle" src="/assets/images/icons/menu/cancel.svg" style="height: 22px; width: 22px;" alt="Closed Status Icon">
            <span class="fw-semibold text-danger" style="font-size: 1.1rem;">{{MY_CONSTANT.MOMENT_STATUS.CLOSED}}</span>
          </span>
        </ng-container>
      </div>
      <!-- <div *ngIf="record?.trainingTitle" class="modern-training-title ms-auto ps-3">
        <span
          class="badge bg-light text-dark px-3 py-2 fs-6 shadow-sm border border-1 border-secondary-subtle">{{record.trainingTitle}}</span>
      </div> -->
    </div>
    <div class="section-divider"></div>
    <!-- Details Grid -->
    <div class="moment-detail-grid elegant-detail-grid">
      <div>
        <div class="info-label">Moment Type</div>
        <div class="info-value">{{record?.isRecurring ? "Recurring" : "Non Recurring"}}</div>
      </div>
      <div *ngIf="record?.createdByUserDetail?.fullName">
        <div class="info-label">Created By</div>
        <div class="info-value">{{record.createdByUserDetail?.fullName}}</div>
      </div>

       
      <div *ngIf="record.createdOn">
        <div class="info-label">Created On</div>
        <div class="info-value">{{moment(record.createdOn).format('DD-MM-YYYY')}}</div>
      </div>
       <div *ngIf="record.closedDate">
        <div class="info-label">End Date</div>
        <div class="info-value">{{moment(record.closedDate).format('DD-MM-YYYY')}}</div>
      </div>
       <div *ngIf="record.updatedOn">
        <div class="info-label">Last Updated On</div>
        <div class="info-value">{{moment(record.updatedOn).format('DD-MM-YYYY')}}</div>
      </div>
           <div *ngIf="record.farmDetail && record.farmDetail.length">
        <div class="info-label">Company</div>
        <div class="info-value">
          <span *ngFor="let farm of record.farmDetail; let last = last">
            {{ farm.name }}<span *ngIf="!last">, </span>
          </span>
        </div>
      </div>
            <div *ngIf="record.courseTitle">
        <div class="info-label">Course</div>
        <div class="info-value">{{record?.courseTitle}}</div>
      </div>
      <div *ngIf="record.trainingTitle">
        <div class="info-label">Training</div>
        <div class="info-value">{{record?.trainingTitle}}</div>
      </div>

      <div *ngIf="record?.description">
        <div class="info-label">Description</div>
        <div class="info-value">{{record?.description}}</div>
      </div>
      
    </div>
    <div class="section-divider"></div>
    <!-- Media and Logs Section -->
    <div class="row g-4 align-items-stretch elegant-media-row">
      <div class="col-md-4 mb-3" *ngIf="record.isRecurring">
        <div class="media-section-title mb-2"><i class="bi bi-journal-text me-2"></i>Logs</div>
        <button
          class="btn btn-primary btn-sm d-flex align-items-center gap-2 rounded-pill px-3 py-2 shadow-sm elegant-btn"
          (click)="openLogsModal(record)">
          <i class="bi bi-list-ul"></i>
          <span>View Logs</span>
        </button>
      </div>
      <div class="col-md-4 mb-3">
        <div class="media-section-title mb-2"><i class="bi bi-camera-video me-2"></i>Moment Video/Image</div>
        <a class="text-decoration-underline text-secondary d-block height-30px elegant-link"
          (click)="openImageOrVideo(record)">
          <img
            [src]="getMomentMediaType(record.mediaUrl, 'icon') === undefined ? '' : '/assets/images/icons/menu/'+ this.getMomentMediaType(record.mediaUrl, 'icon')"
            class="me-1 align-text-top" [ngClass]="{'width-27px': getImageWidthClass(record.mediaUrl)}"
            alt="Moment {{getMomentMediaType(record.mediaUrl, 'title')}} Icon">
          {{getMomentMediaType(record.mediaUrl, 'title')}}
        </a>
      </div>
      <div class="col-md-4 mb-3" *ngIf="!record.isRecurring">
        <div class="media-section-title mb-2"><i class="bi bi-person-video3 me-2"></i>Staff capture Video/Image</div>
        <a *ngIf="(record.userVideo && record.status != MY_CONSTANT.MOMENT_STATUS.OPEN && !record.isRecurring) || (record.recurringLogsCount > 0) else noStaffVideo"
          class="text-decoration-underline d-block height-30px text-secondary elegant-link"
          (click)="!record.isRecurring ? openStaffImageOrVideo(record) : openMomentRecurringModal(record.id)">
          <img
            [src]="!record.isRecurring ? '/assets/images/icons/menu/'+ this.getMomentMediaType(record.userVideo, 'icon') : '/assets/images/icons/menu/list-solid.svg'"
            class="me-1 align-text-top"
            [ngClass]="{'width-27px': !record.isRecurring && this.getImageWidthClass(record.userVideo), 'recurring-moment-list-cls': record.isRecurring}"
            alt="">
          {{!record.isRecurring ? this.getMomentMediaType(record.userVideo,'title') : 'View List'}}
        </a>
        <ng-template #noStaffVideo><a class="d-block height-30px text-muted">-</a></ng-template>
      </div>
    </div>
  </div>
</div>

<!-- Modern, beautiful View Logs Modal -->
<div class="modal fade" id="logsModal" tabindex="-1" aria-labelledby="logsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content logs-modal-content-modern">
      <div class="modal-header border-0 pb-0">
        <h5 class="modal-title fw-bold" id="logsModalLabel">
          <i class="bi bi-journal-text me-2 text-primary"></i>
          {{selectedRecord?.isRecurring ? 'Recurring Logs' : 'Rejected Logs'}}
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body logs-modal-body-modern">
        <div *ngIf="selectedRecord">
          <div *ngIf="selectedRecord.isRecurring && selectedRecord.recurringLogs?.length">
            <table class="table table-hover table-borderless modern-logs-table">
              <thead class="table-light">
                <tr>
                  <th class="text-center">#</th>
                  <th>Name</th>
                  <th>Moment Created Date</th>
                  <th>Staff Uploaded Date</th>
                  <th>Video/Image</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let log of selectedRecord.recurringLogs; let i = index">
                  <td class="text-center">{{ i + 1 }}</td>
                  <td>{{ log.userDetail?.fullName || '-' }}</td>
                  <td>{{ log.momentCreatedDate ? (log.momentCreatedDate | date:'dd-MM-yyyy HH:mm') : '-' }}</td>
                  <td>{{ log.staffUploadedVideoDate ? (log.staffUploadedVideoDate | date:'dd-MM-yyyy HH:mm') : '-' }}
                  </td>
                  <td>
                    <a *ngIf="log.userVideoUrl" href="javascript:void(0)" (click)="showPreview(log.userVideoUrl)"
                      class="text-primary text-decoration-underline">View</a>
                    <span *ngIf="!log.userVideoUrl">-</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div *ngIf="!selectedRecord.isRecurring && selectedRecord.rejectedLogs?.length">
            <table class="table table-hover table-borderless modern-logs-table">
              <thead class="table-light">
                <tr>
                  <th class="text-center">#</th>
                  <th>Name</th>
                  <th>Rejected Date</th>
                  <th>Reason</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let log of selectedRecord.rejectedLogs; let i = index">
                  <td class="text-center">{{ i + 1 }}</td>
                  <td>{{ log.userDetail?.fullName || '-' }}</td>
                  <td>{{ log.rejectedDate ? (log.rejectedDate | date:'dd-MM-yyyy HH:mm') : '-' }}</td>
                  <td>{{ log.reason || '-' }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div
            *ngIf="(!selectedRecord.isRecurring && !selectedRecord.rejectedLogs?.length) || (selectedRecord.isRecurring && !selectedRecord.recurringLogs?.length)"
            class="text-center text-muted py-4">
            No logs found.
          </div>
        </div>
      </div>
      <div class="modal-footer border-0 pt-0">
        <button class="btn btn-primary rounded-pill px-4" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="previewModalLabel">Preview</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body text-center preview-modal-body">
        <ng-container *ngIf="previewType === 'image'">
          <img [src]="previewUrl" class="img-fluid" alt="Preview" />
        </ng-container>
        <ng-container *ngIf="previewType === 'video'">
          <video [src]="previewUrl" controls class="w-100" style="max-height: 400px;">
            <track kind="captions" srclang="en" label="English captions">
          </video>
        </ng-container>
        <ng-container *ngIf="!previewType">
          <p>Cannot preview this file type.</p>
        </ng-container>
      </div>
    </div>
  </div>
</div>

<div class="modal fade modal-xl" id="momentRecurringLogsModal" aria-hidden="true"
  aria-labelledby="momentRecurringLogsModal" tabindex="-1">
  <div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="momentRecurringLogsModalLabel">Moment Recurring Logs List</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <app-recurring-moment-logs [getMomentId]="momentData && momentData"
          *ngIf="momentRecurringLogsModal && momentRecurringLogsModal._isShown"></app-recurring-moment-logs>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="momentVideoOrImageModal" aria-hidden="true" aria-labelledby="momentVideoOrImageModalLabel" tabindex="-1">
  <div class="modal-dialog modal-md modal-dialog-centered">
    <div class="modal-content modern-media-modal-content">
      <div class="modal-header modern-media-modal-header align-items-center">
        <div class="modern-media-header-bar d-flex align-items-center">
          <i class="bi bi-camera-video text-primary me-2" *ngIf="recordData?.mediaType === 'video'"></i>
          <i class="bi bi-image text-primary me-2" *ngIf="recordData?.mediaType === 'image'"></i>
          <span class="modal-title fw-bold" id="momentVideoOrImageModalLabel">{{recordData?.farmIdDetail?.name}}</span>
        </div>
        <button type="button" class="btn-close ms-2" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body modern-media-modal-body">
        <div *ngIf="momentVideoOrImageModal && momentVideoOrImageModal._isShown">
          <div *ngIf="recordData?.mediaType == 'video'">
            <div *ngIf="loadingVideo" class="loading-container-video-training">
              <span class="text-primary" style="font-size:18px; margin-right: 8px">Loading Video</span>
              <div class="spinner-border text-primary">
                <output class="visually-hidden">Loading...</output>
              </div>
            </div>
            <video playsinline autoplay [ngClass]="{'d-none': loadingVideo, 'd-block': !loadingVideo}" controls id="staff-video" class="modern-media-video"></video>
          </div>
          <div *ngIf="recordData?.mediaType == 'image'">
            <img class="img-fluid modern-media-image" [src]="recordData?.mediaUrl" />
          </div>
        </div>
      </div>
      <div class="modal-footer modern-media-modal-footer">
        <button class="btn btn-primary rounded-pill px-4" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Staff Video/Image Modal -->
<div class="modal fade" id="staffVideoOrImageModal" aria-hidden="true" aria-labelledby="staffVideoOrImageModalLabel" tabindex="-1">
  <div class="modal-dialog modal-md modal-dialog-centered">
    <div class="modal-content modern-media-modal-content">
      <div class="modal-header modern-media-modal-header align-items-center">
        <div class="modern-media-header-bar d-flex align-items-center">
          <i class="bi bi-camera-video text-primary me-2" *ngIf="recordData?.mediaType === 'video'"></i>
          <i class="bi bi-image text-primary me-2" *ngIf="recordData?.mediaType === 'image'"></i>
          <span class="modal-title fw-bold" id="staffVideoOrImageModalLabel">Staff Media Preview</span>
        </div>
        <button type="button" class="btn-close ms-2" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body modern-media-modal-body">
        <div *ngIf="staffVideoOrImageModal && staffVideoOrImageModal._isShown">
          <div *ngIf="recordData?.mediaType == 'video'">
            <div *ngIf="loadingVideo" class="loading-container-video-training">
              <span class="text-primary" style="font-size:18px; margin-right: 8px">Loading Video</span>
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
            <video playsinline autoplay [ngClass]="{'d-none': loadingVideo, 'd-block': !loadingVideo}" controls id="staff-video" class="modern-media-video"></video>
          </div>
          <div *ngIf="recordData?.mediaType == 'image'">
            <img class="img-fluid modern-media-image" [src]="recordData?.userVideo" alt="Staff Media Preview" />
          </div>
        </div>
      </div>
      <div class="modal-footer modern-media-modal-footer">
        <button class="btn btn-primary rounded-pill px-4" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>