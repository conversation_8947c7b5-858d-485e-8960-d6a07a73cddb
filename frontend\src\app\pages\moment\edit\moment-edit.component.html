<div data-aos="zoom-in" data-aos-duration="1000" id="userEditComponent" class="site-customer-main-container">
  <div class="dashboard-content-container d-block text-center">
    <form #momentForm="ngForm" novalidate="novalidate" class="text-left d-inline-block">
      <div class="row">

        <div class="mt-2 mb-3">
          <h4 class="fw-bold">{{request.recordId == 0 ? "New Moment" : "Edit Moment"}}</h4>
          <p class="user-edit-msg">Please make sure you fill all the fields before you click on {{request.recordId ==
            0 ? 'save' : 'update'}} button</p>
        </div>
      </div>

      <div class="col-12 col-md-12 mb-4">
        <div class="form-floating">
          <div class="mb-3 form-control select-width ng-select-main-container">
            <ng-select [items]="recurringOptions" bindLabel="label" bindValue="value" name="isRecurring"
              [(ngModel)]="moment.isRecurring" placeholder="Select Moment Type">
            </ng-select>
          </div>
          <label for="isRecurring">Moment Type</label>
        </div>
      </div>

      <div class="col-12 col-md-12 mb-4">
        <div class="form-floating mb-4 w-100">
          <input maxlength="80" [ngClass]="{'is-invalid':(!title.valid || titleTrimError) && onClickValidation}" class="form-control"
            type="text" name="title" #title="ngModel" [(ngModel)]="moment.title" required="required"
            placeholder="{{'Moment.title' | translate}}" (blur)="validateTrimmedField('title', moment.title)">
          <label for="floatingInput">{{"Moment.title" | translate}}</label>
          <div class="error-message" *ngIf="titleTrimError && onClickValidation">
            {{"COMMON.REQUIRED_INPUT_VALIDATION_MESSAGE" | translate}}
          </div>
        </div>
      </div>
      <div class="col-12 col-md-12 mb-4">
        <div class="form-floating form-floating-textarea mb-4 w-100">
          <textarea maxlength="250" [ngClass]="{'is-invalid':(!description.valid || descriptionTrimError) && onClickValidation}"
            class="form-control form-description" name="description" #description="ngModel"
            [(ngModel)]="moment.description" required="required" placeholder="Description"
            id="floatingTextarea2" (blur)="validateTrimmedField('description', moment.description)"></textarea>
          <label for="floatingInput">{{"Moment.description" | translate}}</label>
          <div class="error-message" *ngIf="descriptionTrimError && onClickValidation">
            {{"COMMON.REQUIRED_INPUT_VALIDATION_MESSAGE" | translate}}
          </div>
        </div>
      </div>

      <div *ngIf="moment.isRecurring">
        <mat-form-field class="accent w-100" [class.mat-form-field-invalid]="!fromDate.valid && onClickValidation">
          <mat-label>Start Date</mat-label>
          <input class="mat-datepicker-input" readonly [(ngModel)]="moment.startDate" name="fromDate" matInput
            [min]="minFromDate" [max]="maxFromDate" [matDatepicker]="frompicker"
            (dateInput)="fromDateChange('input', $event)" (focus)="frompicker.open()" required #fromDate="ngModel">
          <mat-icon matDatepickerToggleIcon (click)="clearFromDate($event)">clear</mat-icon>
          <mat-datepicker-toggle matSuffix [for]="frompicker"></mat-datepicker-toggle>
          <mat-datepicker #frompicker></mat-datepicker>
        </mat-form-field>
      </div>
      
      <div *ngIf="moment.isRecurring">
        <mat-form-field class="accent w-100" [class.mat-form-field-invalid]="!toDate.valid && onClickValidation">
          <mat-label>Expiry Date</mat-label>
          <input class="mat-datepicker-input" readonly [(ngModel)]="moment.closedDate" name="toDate" matInput
            [min]="moment.startDate || minToDate" [max]="maxToDate" [matDatepicker]="topicker"
            (dateInput)="toDateChange('input', $event)" (focus)="topicker.open()" required #toDate="ngModel">
          <mat-icon matDatepickerToggleIcon (click)="clearToDate($event)">clear</mat-icon>
          <mat-datepicker-toggle matSuffix [for]="topicker"></mat-datepicker-toggle>
          <mat-datepicker #topicker></mat-datepicker>
        </mat-form-field>
      </div>

      <div *ngIf="!moment.mediaUrl" class="col-12 mb-4 form-control text-center border-2 p-3 border-dark"
        [ngClass]="{'is-invalid': !moment.mediaUrl && onClickValidation}">
        <label id="file-input" class="upload-img-button cursor-pointer"
          [ngClass]="{'d-flex align-items-center justify-content-center cursor-default': isLoader}"><img
            src="/assets/images/icons/menu/upload-icon.svg" class="me-2 upload-icon" alt="">

          {{!isLoader ? ("Moment.UPLOAD_IMAGE_OR_VIDEO" | translate) : fileUploadingMessage }}
          <div *ngIf="isLoader" class="spinner-border ms-2" role="status" style="width: 1.7rem; height: 1.7rem">
            <span class="visually-hidden">Loading...</span>
          </div>
          <input *ngIf="!isLoader" name="mediaUrl" class="d-none" (change)="uploadMomentVideoOrImage($event)"
            id="file-input" type="file" accept="image/png, image/jpg, image/jpeg, video/*" />
        </label>
      </div>

      <div *ngIf="mediaType == 'video' && moment.mediaUrl" [ngStyle]="{'display': !loadingVideo ? 'block': 'none'}"
        class="video-wrapper moment-video-width mb-4">
        <div class="video-container" id="video-container">
          <div class="play-button-wrapper">
            <div (click)="playVideoFromPlayIcon()" title="Play video" class="play-gif circle-play-b-cls"
              id="circle-play-b">
              <!-- SVG Play Button -->
              <svg *ngIf="!videoPlaying" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 80">
                <path d="M40 0a40 40 0 1040 40A40 40 0 0040 0zM26 61.56V18.44L64 40z" />
              </svg>
            </div>
          </div>
          <div class="position-absolute delete-video-container" (click)="removeFile()">
            <i class="bi bi-x"></i>
          </div>
          <video [src]="moment.mediaUrl" playsinline class="mw-100" id="videoId" controlslist="nodownload">
          </video>
        </div>
      </div>

      <div *ngIf="mediaType == 'image' && moment.mediaUrl" class="moment-image-container mb-4">
        <img [src]="moment.mediaUrl" />
        <div class="position-absolute delete-video-container" (click)="removeFile()">
          <i class="bi bi-x"></i>
        </div>
      </div>

      <div class="col-12 col-md-12 mt-2">
        <div class="form-floating">
          <div class="mb-3 form-control select-width ng-select-main-container"
            [ngClass]="{'is-invalid':!farmId.valid && onClickValidation}">
            <ng-select [items]="farms" bindLabel="name" bindValue="id" name="farmId" [(ngModel)]="moment.farmId"
              #farmId="ngModel" (ngModelChange)="onFarmSelect($event)"
              placeholder="Select {{'Farm.objName' | translate}}">
            </ng-select>
          </div>
          <label for="farmId">{{'Farm.objName' | translate}}</label>
        </div>
        <app-validation-message [field]="farmId" [onClickValidation]="onClickValidation"></app-validation-message>
      </div>

      <div class="mt-3" *ngIf="moment.farmId">
        <label>
          <input type="checkbox" [(ngModel)]="showTypeOptions" [ngModelOptions]="{standalone: true}"
            (ngModelChange)="onShowTypeOptionsChange($event)" />
          Do you want to add Course or Training for the selected farm?
        </label>
      </div>
      <div class="col-12 col-md-12 mt-2" *ngIf="showTypeOptions">
        <div class="form-floating">
          <div class="mb-3 form-control select-width ng-select-main-container">
            <ng-select [items]="typeOptions" bindLabel="label" bindValue="value" name="selectedType"
              [(ngModel)]="selectedType" (ngModelChange)="toggleType($event)" placeholder="Select Type">
            </ng-select>
          </div>
          <label for="selectedType">Type</label>
        </div>
      </div>

      <div class="col-12 col-md-12 mt-2" *ngIf="selectedType === 'TRAINING'">
        <div class="form-floating">
          <div class="mb-3 form-control select-width ng-select-main-container"
            [ngClass]="{'is-invalid':!training.valid && onClickValidation}">
            <ng-select [items]="trainings" bindLabel="title" bindValue="id" name="training"
              [(ngModel)]="moment.training" #training="ngModel" placeholder="Select {{'Training.objName' | translate}}">
            </ng-select>
            <div *ngIf="trainings.length === 0" class="text-danger mt-2">No trainings</div>
          </div>
          <label for="training">{{'Training.objName' | translate}}</label>
        </div>
      </div>

      <div class="col-12 col-md-12 mt-2" *ngIf="selectedType === 'COURSE'">
        <div class="form-floating">
          <div class="mb-3 form-control select-width ng-select-main-container"
            [ngClass]="{'is-invalid':!course.valid && onClickValidation}">
            <ng-select [items]="courses" bindLabel="title" bindValue="id" name="course" [(ngModel)]="moment.course"
              #course="ngModel" placeholder="Select {{'Course.objName' | translate}}">
            </ng-select>
            <div *ngIf="courses && courses.length === 0" class="text-danger mt-2">No courses</div>
          </div>
          <label for="course">{{'Course.objName' | translate}}</label>
        </div>
        <app-validation-message [field]="course" [onClickValidation]="onClickValidation"></app-validation-message>
      </div>



      <div class="col-md-12 col-xxl-12 mt-4 d-flex justify-content-end">
        <button (click)="save(momentForm.form)"
          class="btn btn-secondary site-button btn-sm large-button save-button rounded-3">{{request.recordId ==
          0 ? 'SAVE' : 'UPDATE'}}</button>
      </div>
    </form>
  </div>
</div>





<!-- <div class="site-page-container">
  <div class="site-card">
    <form #momentForm="ngForm" novalidate="novalidate">
      <div class="row justify-content-start">
        <div class="col-md-6">
          <div class="form-group">
            <label class="control-label">
              {{"Moment.farmId" | translate}}
            </label>
            <div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
              <ng-select [items]="farms" bindLabel="name" bindValue="id" name="momentFarmId" #momentFarmId="ngModel"
                [(ngModel)]="moment.farmId" required="required" #FarmId="ngModel"
                [ngClass]="{'invalid-field':momentFarmId.invalid && onClickValidation}" required="required"
                placeholder="{{'COMMON.SELECT_OPTION' | translate}} farm">
              </ng-select>
              <span class="input-group-btn" *ngIf="!isPlusButton">
                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('momentFarmIdPopup')"><span
                    class="glyphicon glyphicon-plus"></span></button>
              </span>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="control-label">
              {{"Moment.type" | translate}}
            </label>
            <div class="color-picker-input">
              <input class="form-control" type="text" minlength="0" maxlength="100" name="momentType"
                required="required" [(ngModel)]="moment.type" #Type="ngModel">
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="control-label">
              {{"Moment.description" | translate}}
            </label>
            <textarea class="form-control" rows="5" name="momentDescription" [(ngModel)]="moment.description"
              #Description="ngModel"></textarea>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="control-label">
              {{"Moment.mediaType" | translate}}
            </label>
            <div class="color-picker-input">
              <input class="form-control" type="text" minlength="0" maxlength="255" name="momentMediaType"
                [(ngModel)]="moment.mediaType" #MediaType="ngModel">
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="control-label">
              {{"Moment.mediaUrl" | translate}}
            </label>
            <div class="color-picker-input">
              <input class="form-control" type="text" minlength="0" maxlength="MAX" name="momentMediaUrl"
                [(ngModel)]="moment.mediaUrl" #MediaUrl="ngModel">
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="control-label">
              {{"Moment.assignTrainingId" | translate}}
            </label>
            <div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
              <ng-select [items]="userAssignTrainings" bindLabel="id" bindValue="id" name="momentAssignTrainingId"
                #momentAssignTrainingId="ngModel" [(ngModel)]="moment.assignTrainingId" #AssignTrainingId="ngModel"
                [ngClass]="{'invalid-field':momentAssignTrainingId.invalid && onClickValidation}" required="required"
                placeholder="{{'COMMON.SELECT_OPTION' | translate}} userAssignTraining">
              </ng-select>
              <span class="input-group-btn" *ngIf="!isPlusButton">
                <button class="btn btn-primary" type="button"
                  (click)="loadAssociatedPopup('momentAssignTrainingIdPopup')"><span
                    class="glyphicon glyphicon-plus"></span></button>
              </span>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="control-label">
              {{"Moment.userVideo" | translate}}
            </label>
            <div class="color-picker-input">
              <input class="form-control" type="text" minlength="0" maxlength="MAX" name="momentUserVideo"
                [(ngModel)]="moment.userVideo" #UserVideo="ngModel">
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="control-label">
              {{"Moment.instruction" | translate}}
            </label>
            <div class="color-picker-input">
              <input class="form-control" type="text" minlength="0" maxlength="MAX" name="momentInstruction"
                [(ngModel)]="moment.instruction" #Instruction="ngModel">
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="control-label">
              {{"Moment.completedBy" | translate}}
            </label>
            <input class="form-control" type="number" min="0" name="momentCompletedBy" [(ngModel)]="moment.completedBy"
              #CompletedBy="ngModel">
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="control-label">
              {{"Moment.status" | translate}}
            </label>
            <div class="color-picker-input">
              <input class="form-control" type="text" minlength="0" maxlength="255" name="momentStatus"
                [(ngModel)]="moment.status" #Status="ngModel">
            </div>
          </div>
        </div>
      </div>
    </form>
    <div class="clearfix"></div>
    <div class="col-md-12 no-padding text-right">
      <button title="Save" class="btn btn-primary site-button" type="button" (click)="save(momentForm.form)"
        *ngIf="authService.isAccessible('MOMENT','AddButton')"
        [disabled]="authService.isDisabled('MOMENT','AddButton')">
        {{"COMMON.SAVE" | translate}}
      </button>
      <button title="Cancel" class="btn btn-default site-cancel-button margin-left-10" type="button"
        (click)="navigate()">
        {{"COMMON.CANCEL" | translate}}
      </button>
      <div class="clearfix"></div>
    </div>
    <div class="clearfix"></div>
  </div>
  <div class="clearfix"></div>
</div> -->